# Vue Router 配置使用说明

## 📋 概述

本项目已完成Vue Router的完整配置，包含固定路由和动态路由系统，支持权限控制和菜单管理。

## 🏗️ 架构设计

### 文件结构
```
src/
├── constants/
│   └── routes.ts           # 路由常量定义
├── types/
│   └── router.ts           # 路由类型定义
├── router/
│   ├── index.ts            # 主路由逻辑
│   └── router.ts           # 路由配置文件
├── layout/
│   └── MainLayout.vue      # 主布局组件
├── views/                  # 页面组件
└── examples/
    └── dynamicRoutes.ts    # 动态路由使用示例
```

### 核心特性

1. **固定路由** - 不依赖权限的基础路由（登录、错误页面等）
2. **动态路由** - 根据用户权限动态生成的路由
3. **权限控制** - 支持认证、角色和细粒度权限控制
4. **类型安全** - 完整的TypeScript类型定义
5. **常量管理** - 统一的路由路径和名称管理

## 🚀 使用方法

### 1. 基础路由访问

系统已配置以下固定路由：

- `/` - 根路径，重定向到登录页
- `/login` - 登录页面
- `/dashboard` - 工作台（需要认证）
- `/404` - 页面不存在
- `/403` - 访问被拒绝
- `/500` - 服务器错误

### 2. 登录测试

访问 `http://localhost:5173`，系统会自动跳转到登录页面：

1. **手机登录**：输入任意手机号和验证码，点击"手机登录"
2. **管理员登录**：直接点击"管理员登录"按钮
3. **微信登录**：点击后会显示扫码模态框

登录成功后会跳转到工作台页面。

### 3. 动态路由配置

#### MenuItem 类型定义

```typescript
interface MenuItem {
  path: string              // 路由路径
  name: string             // 路由名称
  title: string            // 页面标题
  icon?: string            // 菜单图标
  component?: string       // 组件路径
  hideInMenu?: boolean     // 是否在菜单中隐藏
  requireAuth?: boolean    // 是否需要认证
  requireAdmin?: boolean   // 是否需要管理员权限
  keepAlive?: boolean      // 是否保持组件活跃
  sort?: number           // 排序权重
  children?: MenuItem[]    // 子菜单
  meta?: Record<string, any> // 扩展元信息
}
```

#### 添加动态路由

```typescript
import { addDynamicRoutesByPermissions } from '@/router'
import type { MenuItem } from '@/types/router'

// 定义菜单项
const menuItems: MenuItem[] = [
  {
    path: '/customer',
    name: 'Customer',
    title: '客户管理',
    icon: 'UserOutlined',
    component: 'customer/CustomerLayout',
    requireAuth: true,
    children: [
      {
        path: '/list',
        name: 'CustomerList',
        title: '客户列表',
        component: 'customer/CustomerList',
        requireAuth: true,
      }
    ]
  }
]

// 添加路由
const userPermissions = ['customer:read']
const userRoles = ['user']
addDynamicRoutesByPermissions(menuItems, userPermissions, userRoles)
```

### 4. 权限控制

#### 认证控制
```typescript
// 需要登录
requireAuth: true

// 不需要登录（默认需要）
requireAuth: false
```

#### 角色控制
```typescript
// 需要管理员权限
requireAdmin: true

// 需要特定角色
meta: {
  roles: ['admin', 'super_admin']
}
```

#### 权限控制
```typescript
// 需要特定权限
meta: {
  permissions: ['customer:read', 'customer:write']
}
```

## 🔧 路由守卫

系统已配置完整的路由守卫：

### 前置守卫
- 设置页面标题
- 检查用户认证状态
- 验证管理员权限
- 处理登录重定向

### 后置守卫
- 路由跳转日志
- 页面访问统计（可扩展）

## 📝 实际应用示例

### 1. 用户登录后添加路由

```typescript
// 登录成功后的处理
async function handleLoginSuccess(userInfo: any) {
  // 保存用户信息
  localStorage.setItem('access_token', userInfo.token)
  localStorage.setItem('user_role', userInfo.role)
  localStorage.setItem('user_permissions', JSON.stringify(userInfo.permissions))
  
  // 获取用户菜单数据
  const menuData = await getUserMenus(userInfo.id)
  
  // 转换为MenuItem格式
  const menuItems = transformToMenuItems(menuData)
  
  // 添加动态路由
  addDynamicRoutesByPermissions(
    menuItems, 
    userInfo.permissions, 
    [userInfo.role]
  )
  
  // 跳转到工作台
  router.push('/dashboard')
}
```

### 2. 退出登录清理路由

```typescript
function handleLogout() {
  // 清除用户信息
  localStorage.clear()
  
  // 清除动态路由
  clearDynamicRoutes()
  
  // 跳转到登录页
  router.push('/login')
}
```

## 🎯 最佳实践

1. **路由常量化**：使用 `ROUTE_PATHS` 和 `ROUTE_NAMES` 常量
2. **类型安全**：使用 TypeScript 类型定义
3. **权限分离**：将权限逻辑与路由配置分离
4. **懒加载**：使用动态导入优化性能
5. **错误处理**：配置完整的错误页面

## 🔍 调试技巧

1. **查看当前路由**：
   ```javascript
   console.log(router.getRoutes())
   ```

2. **检查权限**：
   ```javascript
   import { getUserContext } from '@/router'
   console.log(getUserContext())
   ```

3. **路由跳转日志**：已在后置守卫中配置

## 📚 扩展功能

系统支持以下扩展：

- 面包屑导航
- 菜单树生成
- 路由缓存控制
- 权限验证中间件
- 动态菜单渲染

## 🚨 注意事项

1. 动态路由需要在用户登录后添加
2. 组件路径基于 `src/views/` 目录
3. 权限验证函数需要根据实际业务实现
4. 路由守卫中的认证逻辑需要完善

---

**系统状态**：✅ Vue Router 已完全配置并可正常使用
**测试地址**：http://localhost:5173
**登录方式**：手机登录或管理员登录均可
