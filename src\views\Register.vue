<template>
  <div class="register-page">
    <a-result
      status="info"
      title="用户注册"
      sub-title="注册功能开发中，敬请期待。"
    >
      <template #extra>
        <a-button type="primary" @click="goLogin">
          返回登录
        </a-button>
      </template>
    </a-result>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ROUTE_PATHS } from '@/constants/routes'

const router = useRouter()

const goLogin = () => {
  router.push(ROUTE_PATHS.LOGIN)
}
</script>

<style scoped>
.register-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: #f0f2f5;
}
</style>
