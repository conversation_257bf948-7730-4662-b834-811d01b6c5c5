<template>
  <a-config-provider :theme="themeConfig">
    <div class="theme-provider-root" :style="globalCssVariables">
      <slot></slot>
    </div>
  </a-config-provider>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, computed, watch } from "vue";
import { theme as antTheme } from "ant-design-vue";
import { themeManager, getCurrentThemeConfig } from "@/theme/config";

// 响应式主题配置
const themeConfig = ref(getCurrentThemeConfig());

// 获取主题token
const { token } = antTheme.useToken();

// 全局CSS变量
const globalCssVariables = computed(() => {
  const tokenValue = token.value;

  return {
    // 背景色相关
    "--theme-bg-base": tokenValue.colorBgBase,
    "--theme-bg-container": tokenValue.colorBgContainer,
    "--theme-bg-layout": tokenValue.colorBgLayout,
    "--theme-bg-spotlight": tokenValue.colorBgSpotlight,
    "--theme-bg-elevated": tokenValue.colorBgElevated,

    // 文字色相关
    "--theme-text-base": tokenValue.colorTextBase,
    "--theme-text": tokenValue.colorText,
    "--theme-text-secondary": tokenValue.colorTextSecondary,
    "--theme-text-tertiary": tokenValue.colorTextTertiary,
    "--theme-text-quaternary": tokenValue.colorTextQuaternary,

    // 主题色相关
    "--theme-primary": tokenValue.colorPrimary,
    "--theme-primary-bg": tokenValue.colorPrimaryBg,
    "--theme-primary-border": tokenValue.colorPrimaryBorder,
    "--theme-primary-hover": tokenValue.colorPrimaryHover,
    "--theme-primary-active": tokenValue.colorPrimaryActive,

    // 边框色相关
    "--theme-border": tokenValue.colorBorder,
    "--theme-border-secondary": tokenValue.colorBorderSecondary,

    // 成功色相关
    "--theme-success": tokenValue.colorSuccess,
    "--theme-success-bg": tokenValue.colorSuccessBg,
    "--theme-success-border": tokenValue.colorSuccessBorder,

    // 警告色相关
    "--theme-warning": tokenValue.colorWarning,
    "--theme-warning-bg": tokenValue.colorWarningBg,
    "--theme-warning-border": tokenValue.colorWarningBorder,

    // 错误色相关
    "--theme-error": tokenValue.colorError,
    "--theme-error-bg": tokenValue.colorErrorBg,
    "--theme-error-border": tokenValue.colorErrorBorder,

    // 信息色相关
    "--theme-info": tokenValue.colorInfo,
    "--theme-info-bg": tokenValue.colorInfoBg,
    "--theme-info-border": tokenValue.colorInfoBorder,

    // 其他常用色
    "--theme-fill": tokenValue.colorFill,
    "--theme-fill-secondary": tokenValue.colorFillSecondary,
    "--theme-fill-tertiary": tokenValue.colorFillTertiary,
    "--theme-fill-quaternary": tokenValue.colorFillQuaternary,
  };
});

// 监听主题变化
let unsubscribe: (() => void) | null = null;

onMounted(() => {
  unsubscribe = themeManager.subscribe((theme) => {
    themeConfig.value = getCurrentThemeConfig();
  });

  console.log("ThemeProvider mounted - token:", token.value);
  console.log("Global CSS Variables:", globalCssVariables.value);
});

// 监听CSS变量变化
watch(
  globalCssVariables,
  (newVariables) => {
    console.log("CSS Variables updated:", newVariables);
  },
  { deep: true }
);

onUnmounted(() => {
  if (unsubscribe) {
    unsubscribe();
  }
});
</script>

<style scoped>
.theme-provider-root {
  width: 100%;
  height: 100%;
}
</style>
