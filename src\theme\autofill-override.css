/**
 * 浏览器自动填充样式覆盖 - 最简版本
 * 
 * 解决问题：移除浏览器自动填充时的默认黄色/蓝色背景
 * 保持兼容：完全保留 Ant Design 输入框的原始样式
 * 主题支持：通过 CSS 变量自动适配浅色和深色主题
 */

/* 
 * 核心解决方案：使用 box-shadow 覆盖浏览器默认的自动填充背景色
 * 原理：通过内阴影模拟背景色，避免直接修改 background-color
 * 兼容性：支持 Chrome、Safari、Edge 等 Webkit 内核浏览器
 * 
 * 重要：只对真正自动填充的输入框应用样式，避免影响正常的交互状态
 */
.ant-input:-webkit-autofill {
  /* 使用大范围内阴影覆盖浏览器默认背景 */
  -webkit-box-shadow: 0 0 0 1000px var(--theme-bg-container) inset !important;
  box-shadow: 0 0 0 1000px var(--theme-bg-container) inset !important;
  
  /* 保持文字颜色与主题一致 */
  -webkit-text-fill-color: var(--theme-text) !important;
  
  /* 保持边框颜色与主题一致 */
  /* border-color: var(--theme-border) !important; */
}

/*
 * 自动填充且聚焦状态：在保持自动填充覆盖的同时，添加焦点样式
 * 使用多重阴影：第一层覆盖背景，第二层提供焦点效果
 */
.ant-input:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0 1000px var(--theme-bg-container) inset, 
                      0 0 0 2px var(--theme-primary-bg) !important;
  box-shadow: 0 0 0 1000px var(--theme-bg-container) inset, 
              0 0 0 2px var(--theme-primary-bg) !important;
  border-color: var(--theme-primary) !important;
}

/*
 * 自动填充且悬停状态：保持自动填充样式的同时，恢复悬停效果
 * 只有在确实被自动填充时才应用，不影响正常的悬停行为
 */
.ant-input:-webkit-autofill:hover {
  -webkit-box-shadow: 0 0 0 1000px var(--theme-bg-container) inset !important;
  box-shadow: 0 0 0 1000px var(--theme-bg-container) inset !important;
  -webkit-text-fill-color: var(--theme-text) !important;
  border-color: var(--theme-primary-hover) !important;
}

/*
 * 自动填充且激活状态：保持一致的视觉反馈
 */
.ant-input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 1000px var(--theme-bg-container) inset !important;
  box-shadow: 0 0 0 1000px var(--theme-bg-container) inset !important;
  -webkit-text-fill-color: var(--theme-text) !important;
  border-color: var(--theme-primary-active) !important;
}

/*
 * Firefox 浏览器兼容性支持
 * Firefox 使用不同的自动填充伪类选择器
 */
.ant-input:-moz-autofill {
  background-color: var(--theme-bg-container) !important;
  color: var(--theme-text) !important;
  border-color: var(--theme-border) !important;
}

/*
 * 密码输入框特殊处理
 * 确保密码字段的自动填充也能正确显示
 */
.ant-input[type="password"]:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px var(--theme-bg-container) inset !important;
  box-shadow: 0 0 0 1000px var(--theme-bg-container) inset !important;
  -webkit-text-fill-color: var(--theme-text) !important;
  border-color: var(--theme-border) !important;
}

.ant-input[type="password"]:-webkit-autofill:hover {
  border-color: var(--theme-primary-hover) !important;
}

.ant-input[type="password"]:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0 1000px var(--theme-bg-container) inset, 
                      0 0 0 2px var(--theme-primary-bg) !important;
  box-shadow: 0 0 0 1000px var(--theme-bg-container) inset, 
              0 0 0 2px var(--theme-primary-bg) !important;
  border-color: var(--theme-primary) !important;
}

.ant-input[type="password"]:-webkit-autofill:active {
  border-color: var(--theme-primary-active) !important;
} 