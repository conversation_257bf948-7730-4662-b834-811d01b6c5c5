<template>
  <div class="theme-switcher">
    <a-dropdown>
      <a-button type="text" class="theme-btn">
        <template #icon>
          <BgColorsOutlined />
        </template>
        主题
      </a-button>

      <template #overlay>
        <a-menu
          :selected-keys="[currentTheme]"
          @click="handleThemeChange"
        >
          <a-menu-item
            v-for="theme in availableThemes"
            :key="theme.key"
          >
            <div class="theme-option">
              <div
                class="theme-preview"
                :style="{ background: theme.info.preview }"
              ></div>
              <div class="theme-info">
                <div class="theme-name">{{ theme.info.name }}</div>
                <div class="theme-description">{{ theme.info.description }}</div>
              </div>
            </div>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { BgColorsOutlined } from '@ant-design/icons-vue'
import {
  applyTheme,
  getCurrentTheme,
  getAllThemes,
  getThemeSettingInfo,
  themeManager,
  type ThemeType
} from '@/theme/config'
import { message } from 'ant-design-vue'

// 当前主题
const currentTheme = ref<ThemeType>(getCurrentTheme())

// 可用主题列表
const availableThemes = getAllThemes()

// 主题变化监听器
let unsubscribe: (() => void) | null = null

const handleThemeChange = ({ key }: { key: string }) => {
  const themeType = key as ThemeType
  const themeInfo = getThemeSettingInfo(themeType)

  applyTheme({ themeType })
  message.success(`已切换到${themeInfo.name}`)
}

onMounted(() => {
  // 订阅主题变化
  unsubscribe = themeManager.subscribe((theme) => {
    currentTheme.value = theme
  })
})

onUnmounted(() => {
  // 取消订阅
  if (unsubscribe) {
    unsubscribe()
  }
})
</script>

<style scoped>
.theme-switcher {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.theme-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: var(--theme-bg-elevated);
  border: 1px solid var(--theme-border);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
  transition: all 0.2s;
}

.theme-btn:hover {
  border-color: var(--theme-primary);
  color: var(--theme-primary);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}

.theme-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 4px 0;
  min-width: 200px;
}

.theme-preview {
  width: 20px;
  height: 20px;
  border-radius: 6px;
  border: 1px solid var(--theme-border);
  flex-shrink: 0;
}

.theme-info {
  flex: 1;
}

.theme-name {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
}

.theme-description {
  font-size: 12px;
  color: var(--theme-text-secondary);
  line-height: 1.3;
  margin-top: 2px;
}

/* 暗色主题下的样式调整 */
:global(.ant-theme-dark) .theme-btn {
  background: rgba(20, 20, 20, 0.9);
  border-color: var(--theme-border);
  color: var(--theme-text-base);
}

:global(.ant-theme-dark) .theme-name {
  color: var(--theme-text-base);
}

:global(.ant-theme-dark) .theme-description {
    color: var(--theme-text-secondary);
}
</style>
