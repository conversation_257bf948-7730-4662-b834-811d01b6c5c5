<template>
  <div class="main-layout-wrapper">
    <a-layout class="main-layout" :class="{ 'layout-dark': isDarkMode }">
      <!-- 侧边栏 -->
      <a-layout-sider
        v-model:collapsed="collapsed"
        :trigger="null"
        :width="siderWidth"
        :collapsed-width="siderCollapsedWidth"
        collapsible
        class="main-sider"
        :class="{ 'sider-dark': isDarkMode }"
      >
        <!-- Logo区域 -->
        <div class="logo-container" :class="{ 'logo-collapsed': collapsed }">
          <div class="logo-icon">
            <BankOutlined />
          </div>
          <div v-if="!collapsed" class="logo-text">
            <h3 class="logo-title">iBanko</h3>
            <span class="logo-subtitle">Dashboard</span>
          </div>
        </div>

        <!-- 导航菜单 -->
        <a-menu
          v-model:selectedKeys="selectedKeys"
          v-model:openKeys="openKeys"
          mode="inline"
          class="main-menu"
          :inline-collapsed="collapsed"
        >
          <a-menu-item key="dashboard" @click="navigateTo('/dashboard')">
            <template #icon>
              <DashboardOutlined />
            </template>
            <span>仪表盘</span>
          </a-menu-item>

          <a-sub-menu key="financial" title="财务管理">
            <template #icon>
              <MoneyCollectOutlined />
            </template>
            <a-menu-item key="transactions" @click="navigateTo('/dashboard')">
              <template #icon>
                <TransactionOutlined />
              </template>
              <span>交易记录</span>
            </a-menu-item>
            <a-menu-item key="accounts" @click="navigateTo('/accounts')">
              <template #icon>
                <CreditCardOutlined />
              </template>
              <span>账户管理</span>
            </a-menu-item>
            <a-menu-item key="reports" @click="navigateTo('/reports')">
              <template #icon>
                <BarChartOutlined />
              </template>
              <span>财务报表</span>
            </a-menu-item>
          </a-sub-menu>

          <a-sub-menu key="customers" title="客户管理">
            <template #icon>
              <TeamOutlined />
            </template>
            <a-menu-item key="customer-list" @click="navigateTo('/customers')">
              <template #icon>
                <UserOutlined />
              </template>
              <span>客户列表</span>
            </a-menu-item>
            <a-menu-item key="customer-groups" @click="navigateTo('/customer-groups')">
              <template #icon>
                <UsergroupAddOutlined />
              </template>
              <span>客户分组</span>
            </a-menu-item>
          </a-sub-menu>

          <a-menu-item key="analytics" @click="navigateTo('/analytics')">
            <template #icon>
              <LineChartOutlined />
            </template>
            <span>数据分析</span>
          </a-menu-item>

          <a-menu-item key="settings" @click="navigateTo('/settings')">
            <template #icon>
              <SettingOutlined />
            </template>
            <span>系统设置</span>
          </a-menu-item>
        </a-menu>

        <!-- 侧边栏底部信息 -->
        <div class="sider-footer" v-if="!collapsed">
          <div class="system-info">
            <div class="info-item">
              <span class="info-label">版本</span>
              <span class="info-value">v1.0.0</span>
            </div>
            <div class="info-item">
              <span class="info-label">状态</span>
              <a-tag color="success" size="small">正常</a-tag>
            </div>
          </div>
        </div>
      </a-layout-sider>

      <!-- 主内容区域 -->
      <a-layout class="main-content-layout">
        <!-- 顶部导航栏 -->
        <a-layout-header class="main-header" :class="{ 'header-dark': isDarkMode }">
          <div class="header-left">
            <a-space :size="16" align="center">
              <!-- 折叠按钮 -->
              <a-button
                type="text"
                class="trigger-btn"
                @click="toggleCollapsed"
              >
                <MenuUnfoldOutlined v-if="collapsed" />
                <MenuFoldOutlined v-else />
              </a-button>

              <!-- 面包屑导航 -->
              <a-breadcrumb class="breadcrumb">
                <a-breadcrumb-item>
                  <HomeOutlined />
                </a-breadcrumb-item>
                <a-breadcrumb-item>{{ currentPageTitle }}</a-breadcrumb-item>
              </a-breadcrumb>
            </a-space>
          </div>

          <div class="header-center">
            <!-- 搜索框 -->
            <a-input-search
              v-model:value="searchValue"
              placeholder="搜索功能、客户、交易..."
              class="header-search"
              @search="handleSearch"
            >
              <template #prefix>
                <SearchOutlined />
              </template>
            </a-input-search>
          </div>

          <div class="header-right">
            <a-space :size="12" align="center">
              <!-- 主题切换 -->
              <a-tooltip title="切换主题">
                <a-button
                  type="text"
                  class="header-action-btn"
                  @click="toggleTheme"
                >
                  <ClearOutlined v-if="isDarkMode" />
                  <FireOutlined  v-else />
                </a-button>
              </a-tooltip>

              <!-- 通知 -->
              <a-tooltip title="通知">
                <a-badge :count="notificationCount" size="small">
                  <a-button
                    type="text"
                    class="header-action-btn"
                    @click="showNotifications"
                  >
                    <BellOutlined />
                  </a-button>
                </a-badge>
              </a-tooltip>

              <!-- 全屏切换 -->
              <a-tooltip title="全屏">
                <a-button
                  type="text"
                  class="header-action-btn"
                  @click="toggleFullscreen"
                >
                  <FullscreenOutlined v-if="!isFullscreen" />
                  <FullscreenExitOutlined v-else />
                </a-button>
              </a-tooltip>

              <!-- 用户菜单 -->
              <a-dropdown placement="bottomRight" :trigger="['click']">
                <div class="user-info">
                  <a-space :size="10" align="center">
                    <a-avatar class="user-avatar" :size="32">
                      <template #icon>
                        <UserOutlined />
                      </template>
                    </a-avatar>
                    <div class="user-details" v-if="!isMobile">
                      <div class="user-name">管理员</div>
                      <div class="user-role">系统管理员</div>
                    </div>
                    <DownOutlined class="dropdown-icon" />
                  </a-space>
                </div>

                <template #overlay>
                  <a-menu class="user-menu">
                    <a-menu-item key="profile" @click="navigateTo('/profile')">
                      <a-space :size="10" align="center">
                        <UserOutlined />
                        <span>个人资料</span>
                      </a-space>
                    </a-menu-item>
                    <a-menu-item key="preferences" @click="navigateTo('/preferences')">
                      <a-space :size="10" align="center">
                        <SettingOutlined />
                        <span>偏好设置</span>
                      </a-space>
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="help" @click="showHelp">
                      <a-space :size="10" align="center">
                        <QuestionCircleOutlined />
                        <span>帮助中心</span>
                      </a-space>
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="logout" @click="handleLogout" class="logout-item">
                      <a-space :size="10" align="center">
                        <LogoutOutlined />
                        <span>退出登录</span>
                      </a-space>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </div>
        </a-layout-header>

        <!-- 页面内容 -->
        <a-layout-content class="main-content" :class="{ 'content-dark': isDarkMode }">
          <div class="content-wrapper">
            <router-view v-slot="{ Component, route }">
              <transition name="fade-slide" mode="out-in">
                <component :is="Component" :key="route.path" />
              </transition>
            </router-view>
          </div>
        </a-layout-content>

        <!-- 底部信息 -->
        <a-layout-footer class="main-footer" :class="{ 'footer-dark': isDarkMode }">
          <div class="footer-content">
            <div class="footer-left">
              <span>iBanko Dashboard ©2024</span>
              <a-divider type="vertical" />
              <a href="#" class="footer-link">隐私政策</a>
              <a-divider type="vertical" />
              <a href="#" class="footer-link">服务条款</a>
            </div>
            <div class="footer-right">
              <span class="footer-info">在线用户: {{ onlineUsers }}</span>
              <a-divider type="vertical" />
              <span class="footer-info">系统负载: {{ systemLoad }}%</span>
            </div>
          </div>
        </a-layout-footer>
      </a-layout>
    </a-layout>

    <!-- 通知抽屉 -->
    <a-drawer
      v-model:open="notificationDrawerVisible"
      title="通知中心"
      placement="right"
      :width="400"
      class="notification-drawer"
    >
      <div class="notification-content">
        <a-list
          :data-source="notifications"
          :locale="{ emptyText: '暂无通知' }"
        >
          <template #renderItem="{ item }">
            <a-list-item class="notification-item">
              <a-list-item-meta>
                <template #avatar>
                  <a-avatar :style="{ backgroundColor: item.color }">
                    <component :is="item.icon" />
                  </a-avatar>
                </template>
                <template #title>
                  <span class="notification-title">{{ item.title }}</span>
                  <a-tag v-if="item.isNew" color="red" size="small">新</a-tag>
                </template>
                <template #description>
                  <div class="notification-desc">{{ item.description }}</div>
                  <div class="notification-time">{{ item.time }}</div>
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </div>
    </a-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  BankOutlined,
  DashboardOutlined,
  MoneyCollectOutlined,
  TransactionOutlined,
  CreditCardOutlined,
  BarChartOutlined,
  TeamOutlined,
  UserOutlined,
  UsergroupAddOutlined,
  LineChartOutlined,
  SettingOutlined,
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  HomeOutlined,
  SearchOutlined,
  ClearOutlined,
  FireOutlined,
  BellOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  DownOutlined,
  QuestionCircleOutlined,
  LogoutOutlined
} from '@ant-design/icons-vue'
import { themeState, applyTheme, PreTheme } from '@/theme/index'
import { ROUTE_PATHS } from '@/constants/routes'

// 路由相关
const router = useRouter()
const route = useRoute()

// 主题相关
const currentTheme = ref(themeState.theme.themeKey)
const isDarkMode = computed(() => currentTheme.value === 'dark')

// 布局配置 - 基于设计系统配置
const siderWidth = 220
const siderCollapsedWidth = 80

// 响应式状态
const collapsed = ref(false)
const selectedKeys = ref(['dashboard'])
const openKeys = ref<string[]>(['financial', 'customers'])
const searchValue = ref('')
const notificationDrawerVisible = ref(false)
const isFullscreen = ref(false)
const isMobile = ref(false)

// 模拟数据
const notificationCount = ref(5)
const onlineUsers = ref(128)
const systemLoad = ref(68)

// 当前页面标题
const currentPageTitle = computed(() => {
  const routeMap: Record<string, string> = {
    '/dashboard': '仪表盘',
    '/transactions': '交易记录',
    '/accounts': '账户管理',
    '/reports': '财务报表',
    '/customers': '客户列表',
    '/customer-groups': '客户分组',
    '/analytics': '数据分析',
    '/settings': '系统设置',
    '/profile': '个人资料',
    '/preferences': '偏好设置'
  }
  return routeMap[route.path] || '仪表盘'
})

// 通知数据
const notifications = ref([
  {
    id: 1,
    title: '新的交易记录',
    description: '客户张三完成了一笔 ¥5,000 的存款',
    time: '2分钟前',
    icon: 'MoneyCollectOutlined',
    color: '#52c41a',
    isNew: true
  },
  {
    id: 2,
    title: '系统维护通知',
    description: '系统将于今晚23:00-01:00进行维护',
    time: '1小时前',
    icon: 'SettingOutlined',
    color: '#faad14',
    isNew: true
  },
  {
    id: 3,
    title: '新客户注册',
    description: '客户李四已完成注册并通过审核',
    time: '3小时前',
    icon: 'UserOutlined',
    color: '#1890ff',
    isNew: false
  }
])

// 方法定义
const toggleCollapsed = () => {
  collapsed.value = !collapsed.value
}

const toggleTheme = () => {
  const newThemeKey = isDarkMode.value ? PreTheme.default : PreTheme.dark
  applyTheme({ themeKey: newThemeKey })
  currentTheme.value = newThemeKey
  message.success(`已切换到${newThemeKey === 'dark' ? '深色' : '浅色'}主题`)
}

const navigateTo = (path: string) => {
  router.push(path)
  // 更新选中的菜单项
  const pathToKeyMap: Record<string, string> = {
    '/dashboard': 'dashboard',
    '/transactions': 'transactions',
    '/accounts': 'accounts',
    '/reports': 'reports',
    '/customers': 'customer-list',
    '/customer-groups': 'customer-groups',
    '/analytics': 'analytics',
    '/settings': 'settings'
  }
  const key = pathToKeyMap[path]
  if (key) {
    selectedKeys.value = [key]
  }
}

const handleSearch = (value: string) => {
  if (value.trim()) {
    message.info(`搜索: ${value}`)
    // 这里可以实现实际的搜索逻辑
  }
}

const showNotifications = () => {
  notificationDrawerVisible.value = true
}

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

const showHelp = () => {
  message.info('帮助中心功能开发中...')
}

const handleLogout = () => {
  // 清除本地存储的用户信息
  localStorage.removeItem('access_token')
  localStorage.removeItem('user_role')
  localStorage.removeItem('user_permissions')

  message.success('退出登录成功')

  // 跳转到登录页
  router.push(ROUTE_PATHS.LOGIN)
}

// 检测移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768
  if (isMobile.value) {
    collapsed.value = true
  }
}

// 全屏状态监听
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

// 监听路由变化，更新菜单选中状态
watch(() => route.path, (newPath) => {
  const pathToKeyMap: Record<string, string> = {
    '/dashboard': 'dashboard',
    '/transactions': 'transactions',
    '/accounts': 'accounts',
    '/reports': 'reports',
    '/customers': 'customer-list',
    '/customer-groups': 'customer-groups',
    '/analytics': 'analytics',
    '/settings': 'settings'
  }
  const key = pathToKeyMap[newPath]
  if (key) {
    selectedKeys.value = [key]
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
  document.addEventListener('fullscreenchange', handleFullscreenChange)

  // 模拟实时数据更新
  const updateSystemData = () => {
    systemLoad.value = Math.floor(Math.random() * 30) + 50 // 50-80%
    onlineUsers.value = Math.floor(Math.random() * 50) + 100 // 100-150
  }

  const timer = setInterval(updateSystemData, 30000) // 每30秒更新一次

  onUnmounted(() => {
    window.removeEventListener('resize', checkMobile)
    document.removeEventListener('fullscreenchange', handleFullscreenChange)
    clearInterval(timer)
  })
})
</script>

<style scoped>
/* 基于iBanko设计系统的样式 */
.main-layout-wrapper {
  height: 100vh;
  overflow: hidden;
  font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
}

.main-layout {
  height: 100%;
  background: var(--theme-bg-layout);
  transition: all 0.3s ease;
}

/* 侧边栏样式 */
.main-sider {
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 100;
  background: var(--theme-bg-container);
  border-right: 1px solid var(--theme-border);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-top-right-radius: 16px;
  border-bottom-right-radius: 16px;
  overflow: hidden;
}

.sider-dark {
  background: var(--theme-bg-container);
  border-right-color: var(--theme-border);
}

/* Logo区域 */
.logo-container {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.08) 0%, rgba(82, 196, 26, 0.08) 100%);
  border-bottom: 1px solid var(--theme-border);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.logo-container::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 16px;
  right: 16px;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--theme-border) 50%, transparent 100%);
}

.logo-collapsed {
  padding: 0 8px;
}

.logo-collapsed::after {
  left: 8px;
  right: 8px;
}

.logo-icon {
  font-size: 32px;
  background: linear-gradient(135deg, var(--theme-primary) 0%, #52c41a 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-right: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 2px 4px rgba(24, 144, 255, 0.2));
}

.logo-collapsed .logo-icon {
  margin-right: 0;
  font-size: 28px;
}

.logo-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.logo-title {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(135deg, var(--theme-text) 0%, var(--theme-primary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.logo-subtitle {
  font-size: 12px;
  color: var(--theme-text-secondary);
  margin-top: 2px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* 菜单样式 */
.main-menu {
  border-right: none;
  background: transparent;
  padding: 12px 0;
}

/* 统一的菜单项基础样式 */
.main-menu :deep(.ant-menu-item),
.main-menu :deep(.ant-menu-submenu-title) {
  margin: 3px 12px;
  border-radius: 12px;
  height: 44px;
  line-height: 44px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  color: var(--theme-text);
  background: transparent;
}

/* 菜单项左侧指示器 */
.main-menu :deep(.ant-menu-item::before) {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--theme-primary);
  transform: scaleY(0);
  transition: transform 0.3s ease;
  border-radius: 0 2px 2px 0;
}

.main-menu :deep(.ant-menu-item-selected::before) {
  transform: scaleY(1);
}

/* 子菜单容器样式 */
.main-menu :deep(.ant-menu-submenu) {
  margin: 3px 12px;
}

/* 选中状态样式 */
.main-menu :deep(.ant-menu-item-selected) {
  background: linear-gradient(135deg, var(--theme-primary-bg) 0%, var(--theme-fill-quaternary) 100%);
  color: var(--theme-primary);
  font-weight: 600;
  box-shadow: 0 2px 8px var(--theme-fill-tertiary);
}

/* 悬浮状态样式 */
.main-menu :deep(.ant-menu-item:hover),
.main-menu :deep(.ant-menu-submenu-title:hover) {
  background: var(--theme-bg-elevated);
  transform: translateX(4px);
  box-shadow: 0 2px 8px var(--theme-fill-quaternary);
  color: var(--theme-text);
}

/* 展开的子菜单标题样式 */
.main-menu :deep(.ant-menu-submenu-open > .ant-menu-submenu-title) {
  background: var(--theme-bg-elevated);
  color: var(--theme-primary);
  font-weight: 500;
}

/* 子菜单内容区域样式 */
.main-menu :deep(.ant-menu-sub) {
  background: var(--theme-bg-elevated);
  border-radius: 8px;
  margin: 4px 0px 4px 13px;
  padding: 4px 0;
  box-shadow: inset 0 1px 3px var(--theme-fill-quaternary);
}

/* 子菜单项样式 */
.main-menu :deep(.ant-menu-sub .ant-menu-item) {
  margin: 2px 8px;
  border-radius: 8px;
  height: 36px;
  line-height: 36px;
  padding-left: 40px;
  background: var(--theme-bg-elevated);
  color: var(--theme-text-secondary);
  font-size: 13px;
}

.main-menu :deep(.ant-menu-sub .ant-menu-item:hover) {
  background: var(--theme-fill-quaternary);
  color: var(--theme-text);
  transform: translateX(2px);
  box-shadow: 0 1px 4px var(--theme-fill-tertiary);
}

.main-menu :deep(.ant-menu-sub .ant-menu-item-selected) {
  background: var(--theme-primary-bg);
  color: var(--theme-primary);
  font-weight: 500;
  box-shadow: 0 1px 4px var(--theme-fill-secondary);
}

.main-menu :deep(.ant-menu-sub .ant-menu-item::before) {
  display: none;
}

/* 侧边栏底部 */
.sider-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  border-top: 1px solid var(--theme-border);
  background: var(--theme-bg-container);
}

.system-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.info-label {
  color: var(--theme-text-secondary);
}

.info-value {
  color: var(--theme-text);
  font-weight: 500;
}

/* 主内容区域 */
.main-content-layout {
  margin-left: 220px;
  transition: margin-left 0.3s ease;
  background: transparent;
}

.main-layout :deep(.ant-layout-sider-collapsed) + .main-content-layout {
  margin-left: 80px;
}

/* 顶部导航栏 */
.main-header {
  background: var(--theme-bg-container);
  border-bottom: 1px solid var(--theme-border);
  padding: 0 20px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 10;
  backdrop-filter: blur(8px);
}

.header-dark {
  background: var(--theme-bg-container);
  border-bottom-color: var(--theme-border);
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.header-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  max-width: 400px;
  margin: 0 20px;
}

.header-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
  min-width: 0;
}

.trigger-btn {
  font-size: 18px;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.trigger-btn:hover {
  background: var(--theme-bg-elevated);
  color: var(--theme-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.breadcrumb {
  margin: 0;
  font-size: 14px;
}

.header-search {
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
  max-width: 320px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.header-search:hover,
.header-search:focus-within {
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  transform: translateY(-1px);
}

.header-action-btn {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.header-action-btn:hover {
  background: var(--theme-bg-elevated);
  color: var(--theme-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

/* 用户信息 */
.user-info {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 6px 12px;
  border-radius: 12px;
  border: 1px solid transparent;
}

.user-info:hover {
  background: var(--theme-bg-elevated);
  border-color: var(--theme-border);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.user-avatar {
  background: linear-gradient(135deg, var(--theme-primary) 0%, #52c41a 100%);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  transition: all 0.3s ease;
}

.user-info:hover .user-avatar {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  min-height: 32px;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--theme-text);
  line-height: 1.3;
  margin: 0;
}

.user-role {
  font-size: 12px;
  color: var(--theme-text-secondary);
  line-height: 1.2;
  margin: 0;
  margin-top: 1px;
}

.dropdown-icon {
  font-size: 12px;
  color: var(--theme-text-tertiary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-info:hover .dropdown-icon {
  transform: rotate(180deg);
  color: var(--theme-primary);
}

/* 用户菜单 */
.user-menu {
  min-width: 180px;
  border-radius: 12px;
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--theme-border);
  overflow: hidden;
}

.user-menu :deep(.ant-menu-item) {
  padding: 12px 16px;
  margin: 4px 8px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
}

.user-menu :deep(.ant-menu-item:hover) {
  background: var(--theme-bg-elevated);
  transform: translateX(2px);
}

.logout-item {
  color: #f5222d !important;
}

.logout-item:hover {
  background: rgba(245, 34, 45, 0.08) !important;
  color: #f5222d !important;
}

/* 主内容区域 */
.main-content {
  padding: 20px;
  background: var(--theme-bg-layout);
  min-height: calc(100vh - 64px - 70px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.content-dark {
  background: var(--theme-bg-layout);
}

.content-wrapper {
  background: var(--theme-bg-container);
  border-radius: 16px;
  min-height: 100%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid var(--theme-border);
  backdrop-filter: blur(8px);
}

/* 页面切换动画 */
.fade-slide-enter-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-slide-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(30px) scale(0.98);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-30px) scale(0.98);
}

/* 底部信息 */
.main-footer {
  background: var(--theme-bg-container);
  border-top: 1px solid var(--theme-border);
  padding: 16px 24px;
  text-align: center;
}

.footer-dark {
  background: var(--theme-bg-container);
  border-top-color: var(--theme-border);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.footer-left,
.footer-right {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--theme-text-secondary);
}

.footer-link {
  color: var(--theme-text-secondary);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: var(--theme-primary);
}

.footer-info {
  font-size: 13px;
}

/* 通知抽屉 */
.notification-drawer :deep(.ant-drawer-body) {
  padding: 0;
}

.notification-content {
  height: 100%;
}

.notification-item {
  padding: 16px;
  border-bottom: 1px solid var(--theme-border);
  transition: background 0.3s ease;
}

.notification-item:hover {
  background: var(--theme-bg-layout);
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--theme-text);
  margin-right: 8px;
}

.notification-desc {
  font-size: 13px;
  color: var(--theme-text-secondary);
  margin-bottom: 4px;
  line-height: 1.4;
}

.notification-time {
  font-size: 12px;
  color: var(--theme-text-tertiary);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content-layout {
    margin-left: 220px;
  }

  .main-layout :deep(.ant-layout-sider-collapsed) + .main-content-layout {
    margin-left: 80px;
  }

  .header-center {
    max-width: 300px;
    margin: 0 16px;
  }

  .header-search {
    max-width: 280px;
  }
}

@media (max-width: 992px) {
  .header-center {
    display: none;
  }

  .header-left,
  .header-right {
    flex: auto;
  }

  .main-header {
    padding: 0 16px;
  }
}

@media (max-width: 768px) {
  .main-content-layout {
    margin-left: 0;
  }

  .main-layout :deep(.ant-layout-sider-collapsed) + .main-content-layout {
    margin-left: 0;
  }

  .main-sider {
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 0;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  .main-sider:not(.ant-layout-sider-collapsed) {
    transform: translateX(0);
  }

  .main-header {
    padding: 0 12px;
    border-radius: 0;
  }

  .main-content {
    padding: 12px;
  }

  .content-wrapper {
    border-radius: 12px;
  }

  .user-details {
    display: none;
  }

  .footer-content {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .breadcrumb {
    display: none;
  }

  .trigger-btn,
  .header-action-btn {
    width: 36px;
    height: 36px;
    border-radius: 8px;
  }
}

@media (max-width: 576px) {
  .header-left :deep(.ant-space) {
    gap: 8px !important;
  }

  .header-right :deep(.ant-space) {
    gap: 6px !important;
  }

  .main-content {
    padding: 8px;
  }

  .content-wrapper {
    border-radius: 8px;
  }

  .notification-drawer {
    width: 100vw !important;
  }

  .user-info {
    padding: 4px 8px;
    border-radius: 8px;
  }

  .main-menu :deep(.ant-menu-item),
  .main-menu :deep(.ant-menu-submenu-title) {
    height: 40px;
    line-height: 40px;
    margin: 2px 8px;
    border-radius: 8px;
  }
}

/* 深色主题特殊样式 */
:global(.ant-theme-dark) .main-layout {
  background: var(--theme-bg-layout);
}

:global(.ant-theme-dark) .main-sider {
  background: var(--theme-bg-container);
  border-right-color: var(--theme-border);
}

:global(.ant-theme-dark) .main-header {
  background: var(--theme-bg-container);
  border-bottom-color: var(--theme-border);
}

:global(.ant-theme-dark) .main-content {
  background: var(--theme-bg-layout);
}

:global(.ant-theme-dark) .content-wrapper {
  background: var(--theme-bg-container);
  border-color: var(--theme-border);
}

:global(.ant-theme-dark) .main-footer {
  background: var(--theme-bg-container);
  border-top-color: var(--theme-border);
}

:global(.ant-theme-dark) .logo-container {
  background: linear-gradient(135deg, var(--theme-success-bg) 0%, var(--theme-primary-bg) 100%);
  border-bottom-color: var(--theme-border);
}

:global(.ant-theme-dark) .sider-footer {
  background: var(--theme-bg-layout);
  border-top-color: var(--theme-border);
}

/* 深色主题菜单增强样式 */
:global(.ant-theme-dark) .main-menu :deep(.ant-menu-item:hover),
:global(.ant-theme-dark) .main-menu :deep(.ant-menu-submenu-title:hover) {
  background: var(--theme-fill-quaternary);
  box-shadow: 0 2px 8px var(--theme-fill-tertiary);
}

:global(.ant-theme-dark) .main-menu :deep(.ant-menu-item-selected) {
  background: linear-gradient(135deg, var(--theme-primary-bg) 0%, var(--theme-fill-quaternary) 100%);
  box-shadow: 0 2px 8px var(--theme-fill-secondary);
}

:global(.ant-theme-dark) .main-menu :deep(.ant-menu-sub) {
  background: var(--theme-fill-quaternary);
  box-shadow: inset 0 1px 3px var(--theme-fill-tertiary);
}

:global(.ant-theme-dark) .main-menu :deep(.ant-menu-sub .ant-menu-item:hover) {
  background: var(--theme-fill-tertiary);
  box-shadow: 0 1px 4px var(--theme-fill-secondary);
}

:global(.ant-theme-dark) .main-menu :deep(.ant-menu-sub .ant-menu-item-selected) {
  background: var(--theme-primary-bg);
  box-shadow: 0 1px 4px var(--theme-fill);
}

/* 滚动条样式 */
.main-sider :deep(.ant-layout-sider-children) {
  overflow-y: auto;
  overflow-x: hidden;
}

.main-sider :deep(.ant-layout-sider-children)::-webkit-scrollbar {
  width: 6px;
}

.main-sider :deep(.ant-layout-sider-children)::-webkit-scrollbar-track {
  background: transparent;
}

.main-sider :deep(.ant-layout-sider-children)::-webkit-scrollbar-thumb {
  background: var(--theme-border);
  border-radius: 3px;
}

.main-sider :deep(.ant-layout-sider-children)::-webkit-scrollbar-thumb:hover {
  background: var(--theme-text-tertiary);
}

/* 动画增强 */
.main-layout * {
  transition-duration: 0.3s;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 焦点样式 */
.trigger-btn:focus,
.header-action-btn:focus {
  outline: 2px solid var(--theme-primary);
  outline-offset: 2px;
}

/* 加载状态 */
.main-layout.loading {
  pointer-events: none;
}

.main-layout.loading::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

:global(.ant-theme-dark) .main-layout.loading::after {
  background: rgba(0, 0, 0, 0.8);
}
</style>