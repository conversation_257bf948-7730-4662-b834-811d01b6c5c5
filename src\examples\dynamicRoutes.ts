import type { MenuItem } from '@/types/router'
import { addDynamicRoutesByPermissions } from '@/router'

/**
 * 示例：动态路由菜单配置
 * 这个文件展示了如何定义MenuItem数组并使用动态路由功能
 */

// 示例菜单项配置
export const exampleMenuItems: MenuItem[] = [
  {
    path: '/customer',
    name: 'Customer',
    title: '客户管理',
    icon: 'UserOutlined',
    component: 'customer/CustomerLayout', // 对应 src/views/customer/CustomerLayout.vue
    requireAuth: true,
    sort: 1,
    children: [
      {
        path: '/list',
        name: 'CustomerList',
        title: '客户列表',
        icon: 'UnorderedListOutlined',
        component: 'customer/CustomerList',
        requireAuth: true,
        keepAlive: true,
      },
      {
        path: '/add',
        name: 'CustomerAdd',
        title: '新增客户',
        icon: 'PlusOutlined',
        component: 'customer/CustomerAdd',
        requireAuth: true,
        hideInMenu: true, // 在菜单中隐藏，但路由存在
      },
      {
        path: '/detail/:id',
        name: 'CustomerDetail',
        title: '客户详情',
        component: 'customer/CustomerDetail',
        requireAuth: true,
        hideInMenu: true,
        meta: {
          breadcrumb: ['客户管理', '客户列表', '客户详情'],
        },
      },
    ],
  },
  {
    path: '/appointment',
    name: 'Appointment',
    title: '预约管理',
    icon: 'CalendarOutlined',
    component: 'appointment/AppointmentLayout',
    requireAuth: true,
    sort: 2,
    children: [
      {
        path: '/list',
        name: 'AppointmentList',
        title: '预约列表',
        icon: 'UnorderedListOutlined',
        component: 'appointment/AppointmentList',
        requireAuth: true,
        keepAlive: true,
      },
      {
        path: '/calendar',
        name: 'AppointmentCalendar',
        title: '预约日历',
        icon: 'CalendarOutlined',
        component: 'appointment/AppointmentCalendar',
        requireAuth: true,
        keepAlive: true,
      },
    ],
  },
  {
    path: '/project',
    name: 'Project',
    title: '项目管理',
    icon: 'ProjectOutlined',
    component: 'project/ProjectLayout',
    requireAuth: true,
    sort: 3,
    children: [
      {
        path: '/list',
        name: 'ProjectList',
        title: '项目列表',
        icon: 'UnorderedListOutlined',
        component: 'project/ProjectList',
        requireAuth: true,
      },
      {
        path: '/category',
        name: 'ProjectCategory',
        title: '项目分类',
        icon: 'AppstoreOutlined',
        component: 'project/ProjectCategory',
        requireAuth: true,
        meta: {
          permissions: ['project:category:read'], // 需要特定权限
        },
      },
    ],
  },
  {
    path: '/staff',
    name: 'Staff',
    title: '员工管理',
    icon: 'TeamOutlined',
    component: 'staff/StaffLayout',
    requireAuth: true,
    requireAdmin: true, // 需要管理员权限
    sort: 4,
    children: [
      {
        path: '/list',
        name: 'StaffList',
        title: '员工列表',
        icon: 'UnorderedListOutlined',
        component: 'staff/StaffList',
        requireAuth: true,
        requireAdmin: true,
      },
      {
        path: '/role',
        name: 'StaffRole',
        title: '角色管理',
        icon: 'SafetyOutlined',
        component: 'staff/StaffRole',
        requireAuth: true,
        requireAdmin: true,
        meta: {
          roles: ['admin', 'super_admin'], // 需要特定角色
        },
      },
    ],
  },
  {
    path: '/finance',
    name: 'Finance',
    title: '财务管理',
    icon: 'MoneyCollectOutlined',
    component: 'finance/FinanceLayout',
    requireAuth: true,
    sort: 5,
    meta: {
      permissions: ['finance:read'], // 需要财务权限
    },
    children: [
      {
        path: '/income',
        name: 'FinanceIncome',
        title: '收入管理',
        icon: 'RiseOutlined',
        component: 'finance/FinanceIncome',
        requireAuth: true,
        meta: {
          permissions: ['finance:income:read'],
        },
      },
      {
        path: '/expense',
        name: 'FinanceExpense',
        title: '支出管理',
        icon: 'FallOutlined',
        component: 'finance/FinanceExpense',
        requireAuth: true,
        meta: {
          permissions: ['finance:expense:read'],
        },
      },
      {
        path: '/report',
        name: 'FinanceReport',
        title: '财务报表',
        icon: 'BarChartOutlined',
        component: 'finance/FinanceReport',
        requireAuth: true,
        requireAdmin: true,
        meta: {
          permissions: ['finance:report:read'],
        },
      },
    ],
  },
]

/**
 * 使用示例：如何在应用中添加动态路由
 */
export function setupExampleDynamicRoutes() {
  // 模拟用户权限和角色
  const userPermissions = [
    'project:category:read',
    'finance:read',
    'finance:income:read',
    'finance:expense:read',
  ]
  
  const userRoles = ['user'] // 普通用户，没有admin权限

  // 根据权限添加动态路由
  addDynamicRoutesByPermissions(exampleMenuItems, userPermissions, userRoles)
  
  console.log('动态路由已添加，用户权限:', userPermissions)
  console.log('用户角色:', userRoles)
}

/**
 * 管理员示例：添加所有路由
 */
export function setupAdminDynamicRoutes() {
  // 管理员拥有所有权限
  const adminPermissions = [
    'project:category:read',
    'finance:read',
    'finance:income:read',
    'finance:expense:read',
    'finance:report:read',
  ]
  
  const adminRoles = ['admin', 'super_admin']

  // 根据权限添加动态路由
  addDynamicRoutesByPermissions(exampleMenuItems, adminPermissions, adminRoles)
  
  console.log('管理员动态路由已添加，权限:', adminPermissions)
  console.log('管理员角色:', adminRoles)
}

/**
 * 在实际应用中的使用方式：
 * 
 * 1. 用户登录成功后，从后端获取用户的菜单权限数据
 * 2. 将后端返回的菜单数据转换为MenuItem[]格式
 * 3. 调用addDynamicRoutesByPermissions()添加路由
 * 
 * 示例代码：
 * 
 * // 登录成功后
 * const userMenuData = await getUserMenus() // 从后端获取
 * const menuItems = transformToMenuItems(userMenuData) // 转换格式
 * addDynamicRoutesByPermissions(menuItems, userPermissions, userRoles)
 */
