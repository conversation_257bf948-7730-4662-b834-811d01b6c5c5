# 全局主题系统使用指南

## 概述

本系统提供了一套完整的主题管理解决方案，支持在任何 Vue 组件中使用响应式的主题配置和全局 CSS 变量。

## 系统架构

### 1. 主题管理器 (`themeManager`)
- 负责主题状态管理
- 提供主题切换功能
- 支持主题变化订阅

### 2. Vue 组合式函数 (`useTheme`)
- 在组件内部使用 `theme.useToken()` 获取实时 token
- 提供响应式的主题配置
- 返回完整的主题信息

### 3. 全局 CSS 变量系统
- 在 `ThemeProvider` 中自动设置 CSS 变量
- 所有组件都可以通过 CSS 变量获取主题色
- 主题切换时自动更新

### 4. 全局样式工具类
- 提供预定义的主题样式类
- 支持背景色、文字色、边框色等
- 响应式主题切换

## 使用方法

### 方法1: 在 Vue 组件中使用 useTheme()

在任何 Vue 组件的 `setup()` 函数中：

\`\`\`vue
<template>
  <div class="my-component">
    <h1>当前主题: {{ currentThemeSettingInfo.name }}</h1>
    <p style="{ color: token.colorPrimary }">主题色文字</p>
    <div :style="{ backgroundColor: token.colorBgContainer }">
      背景色容器
    </div>
  </div>
</template>

<script setup lang="ts">
import { useTheme } from '@/theme/config';

// 获取响应式主题配置
const { 
  currentTheme,           // 当前主题类型
  currentThemeConfig,     // Ant Design 主题配置
  currentThemeColorInfo,  // 主题颜色信息
  currentThemeSettingInfo,// 主题设置信息
  token,                  // 实时的 Ant Design token
  setTheme,              // 设置主题函数
  getAllThemes           // 获取所有主题函数
} = useTheme();

// 监听主题变化
watch(token, (newToken) => {
  console.log('获取到最新的主题token:', newToken);
  console.log('背景色:', newToken.colorBgBase);
  console.log('文字色:', newToken.colorTextBase);
  console.log('主题色:', newToken.colorPrimary);
});

// 切换主题
const switchToDark = () => {
  setTheme('dark');
};
</script>
\`\`\`

### 方法2: 使用全局 CSS 变量

在任何组件的 CSS 中直接使用：

\`\`\`vue
<template>
  <div class="themed-container">
    <div class="custom-card">
      <h2 class="card-title">自定义卡片</h2>
      <p class="card-content">这是使用 CSS 变量的内容</p>
    </div>
  </div>
</template>

<style scoped>
.themed-container {
  background-color: var(--theme-bg-layout);
  color: var(--theme-text);
  padding: 20px;
}

.custom-card {
  background-color: var(--theme-bg-container);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px var(--theme-fill-quaternary);
}

.card-title {
  color: var(--theme-primary);
  margin-bottom: 12px;
}

.card-content {
  color: var(--theme-text-secondary);
}
</style>
\`\`\`

### 方法3: 使用全局样式工具类

直接在模板中使用预定义的样式类：

\`\`\`vue
<template>
  <div class="theme-page">
    <!-- 背景色工具类 -->
    <div class="bg-container theme-transition" style="padding: 16px;">
      <h2 class="text-primary">主标题</h2>
      <p class="text-secondary">次要文本</p>
      
      <!-- 主题卡片 -->
      <div class="theme-card">
        <h3 class="text-theme-primary">卡片标题</h3>
        <p class="text-primary">卡片内容</p>
      </div>
      
      <!-- 状态色演示 -->
      <div class="status-demo">
        <span class="text-success">成功文本</span>
        <span class="text-warning">警告文本</span>
        <span class="text-error">错误文本</span>
        <span class="text-info">信息文本</span>
      </div>
      
      <!-- 按钮样式 -->
      <button class="theme-button">主题按钮</button>
      <button class="theme-button-outline">轮廓按钮</button>
      
      <!-- 表单元素 -->
      <input class="theme-input" placeholder="主题输入框" />
    </div>
  </div>
</template>
\`\`\`

## 可用的 CSS 变量

### 背景色变量
- `--theme-bg-base`: 基础背景色
- `--theme-bg-container`: 容器背景色
- `--theme-bg-layout`: 布局背景色
- `--theme-bg-elevated`: 提升背景色

### 文字色变量
- `--theme-text-base`: 基础文字色
- `--theme-text`: 主要文字色
- `--theme-text-secondary`: 次要文字色
- `--theme-text-tertiary`: 第三级文字色

### 主题色变量
- `--theme-primary`: 主题色
- `--theme-primary-bg`: 主题色背景
- `--theme-primary-hover`: 主题色悬停态
- `--theme-primary-active`: 主题色激活态

### 状态色变量
- `--theme-success`: 成功色
- `--theme-warning`: 警告色
- `--theme-error`: 错误色
- `--theme-info`: 信息色

### 边框色变量
- `--theme-border`: 主要边框色
- `--theme-border-secondary`: 次要边框色

## 可用的工具类

### 背景色工具类
- `.bg-base`, `.bg-container`, `.bg-layout`
- `.bg-primary`, `.bg-success`, `.bg-warning`, `.bg-error`

### 文字色工具类
- `.text-primary`, `.text-secondary`, `.text-tertiary`
- `.text-theme-primary`, `.text-success`, `.text-warning`, `.text-error`

### 组合样式类
- `.theme-card`: 主题卡片样式
- `.theme-button`: 主题按钮样式
- `.theme-input`: 主题输入框样式
- `.theme-page`: 主题页面样式
- `.theme-transition`: 主题切换过渡动画

## 主题切换示例

\`\`\`vue
<template>
  <div class="theme-switcher">
    <button 
      v-for="theme in allThemes" 
      :key="theme.key"
      @click="switchTheme(theme.key)"
      :class="{ active: currentTheme === theme.key }"
      class="theme-option"
    >
      {{ theme.info.name }}
    </button>
  </div>
</template>

<script setup lang="ts">
import { useTheme } from '@/theme/config';

const { currentTheme, setTheme, getAllThemes } = useTheme();
const allThemes = getAllThemes();

const switchTheme = (themeType) => {
  setTheme(themeType);
};
</script>
\`\`\`

## 注意事项

1. **useTheme() 只能在 Vue 组件的 setup() 中使用**
   - 因为内部使用了 `theme.useToken()`
   - 不能在普通的 JavaScript 函数中调用

2. **CSS 变量在所有组件中都可用**
   - 由 `ThemeProvider` 自动管理
   - 主题切换时自动更新

3. **响应式更新**
   - 所有使用 CSS 变量的样式会自动响应主题切换
   - 使用 `useTheme()` 的组件会自动重新渲染

4. **性能优化**
   - CSS 变量比 JavaScript 样式绑定性能更好
   - 建议优先使用 CSS 变量而不是动态样式绑定

## 扩展和自定义

如果需要添加更多的主题变量或样式类，可以：

1. 在 `ThemeProvider.vue` 中添加更多 CSS 变量
2. 在 `global.css` 中添加更多工具类
3. 在 `config.ts` 中扩展主题配置

这样就能构建一个完整的、响应式的主题系统，满足各种使用场景的需求。 