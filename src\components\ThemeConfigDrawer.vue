<template>
  <div class="theme-config-trigger">
    <!-- 固定在右下角的主题配置按钮 -->
    <a-button
      type="primary"
      shape="circle"
      class="theme-config-button"
      @click="showDrawer"
    >
      <template #icon><SettingOutlined /></template>
    </a-button>

    <!-- 主题配置抽屉 -->
    <a-drawer
      title="主题配置"
      placement="right"
      :open="drawerVisible"
      @close="closeDrawer"
      width="320"
    >
      <div class="theme-config-content">
        <!-- 当前主题信息卡片 -->
        <a-card>
          <small class="text-secondary">
            当前主题: {{ currentThemeSettingInfo.name }}
          </small>
          <div
            class="theme-colors-demo"
            style="margin-top: 8px; display: flex; gap: 8px"
          >
            <span class="color-demo bg-primary" title="主题色"></span>
            <span class="color-demo bg-success" title="成功色"></span>
            <span class="color-demo bg-warning" title="警告色"></span>
            <span class="color-demo bg-error" title="错误色"></span>
          </div>
        </a-card>

        <!-- 主题选择 -->
        <div class="config-section">
          <h3>选择主题</h3>
          <div class="theme-options">
            <template #overlay>
              <a-menu
                :selected-keys="[selectedTheme]"
                @click="handleThemeChange"
              >
                <a-menu-item v-for="theme in availableThemes" :key="theme.key">
                  <div class="theme-option">
                    <div
                      class="theme-preview"
                      :style="{ background: theme.info.preview }"
                    ></div>
                    <div class="theme-info">
                      <div class="theme-name">{{ theme.info.name }}</div>
                      <div class="theme-description">
                        {{ theme.info.description }}
                      </div>
                    </div>
                  </div>
                </a-menu-item>
              </a-menu>
            </template>
          </div>
        </div>

        <!-- 主题色配置 -->
        <div class="config-section">
          <h3>主题色配置</h3>
          <div class="color-picker-section">
            <div
              class="color-preview"
              :style="{ backgroundColor: customColorPrimary }"
            ></div>
            <a-input
              v-model:value="customColorPrimary"
              placeholder="#1f74ec"
              addon-before="主题色"
              @change="handleColorChange"
            >
              <template #addonAfter>
                <div class="color-input-addon">
                  <input
                    type="color"
                    :value="customColorPrimary"
                    @input="handleColorPickerChange"
                  />
                </div>
              </template>
            </a-input>
          </div>

          <!-- 应用自定义主题按钮 -->
          <a-button
            type="primary"
            block
            style="margin-top: 16px"
            @click="applyCustomTheme"
            :disabled="!isCustomColorChanged"
          >
            应用自定义主题
          </a-button>

          <!-- 重置为默认主题色按钮 -->
          <a-button
            type="default"
            block
            style="margin-top: 8px"
            @click="resetToDefaultColor"
            :disabled="!isCustomColorChanged"
          >
            重置为默认主题色
          </a-button>
        </div>
      </div>
    </a-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { SettingOutlined } from "@ant-design/icons-vue";
import { message } from "ant-design-vue";
import {
  useTheme,
  getAllThemes,
  applyTheme,
  type ThemeType,
  defaultTokens,
} from "@/theme/config";

// 抽屉可见性状态
const drawerVisible = ref(false);

// 获取主题相关信息
const { currentTheme, currentThemeSettingInfo, token } = useTheme();

// 获取所有可用主题
const availableThemes = getAllThemes();

// 当前选中的主题
const selectedTheme = ref<ThemeType>(currentTheme.value);

// 自定义主题色
const customColorPrimary = ref(defaultTokens.colorPrimary);

// 计算自定义颜色是否已更改
const isCustomColorChanged = computed(() => {
  return customColorPrimary.value !== defaultTokens.colorPrimary;
});

// 显示抽屉
const showDrawer = () => {
  drawerVisible.value = true;
};

// 关闭抽屉
const closeDrawer = () => {
  drawerVisible.value = false;
};

// 处理主题切换
const handleThemeChange = (e: any) => {
  const themeType = e.target.value as ThemeType;
  selectedTheme.value = themeType;
  applyTheme({ themeType, token: customColorPrimary.value });
  message.success(`已切换到${currentThemeSettingInfo.value.name}`);
};

// 处理颜色输入变化
const handleColorChange = (e: any) => {
  const colorValue = e.target.value;
  // 验证颜色格式
  if (/^#[0-9A-Fa-f]{6}$/.test(colorValue)) {
    customColorPrimary.value = colorValue;
  }
};

// 处理颜色选择器变化
const handleColorPickerChange = (e: any) => {
  customColorPrimary.value = e.target.value;
};

// 应用自定义主题
const applyCustomTheme = () => {
  // 创建并应用自定义主题
  applyTheme({ themeType: "custom", token: customColorPrimary.value });
  message.success("自定义主题已应用");

  // 切换到自定义主题
  selectedTheme.value = "custom";
};

// 重置为默认主题色
const resetToDefaultColor = () => {
  customColorPrimary.value = defaultTokens.colorPrimary;
  applyTheme({ themeType: "custom", token: defaultTokens.colorPrimary });
  message.success("已重置为默认主题色");
};

// 组件挂载时，从当前主题获取主题色
onMounted(() => {
  // 如果当前是自定义主题，获取其主题色
  if (currentTheme.value === "custom") {
    const savedCustomColor = localStorage.getItem("custom-theme-primary-color");
    if (savedCustomColor) {
      customColorPrimary.value = savedCustomColor;
    }
  }
});
</script>

<style scoped>
.theme-config-trigger {
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 1000;
}

.theme-config-button {
  width: 48px;
  height: 48px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 6px 16px 0 rgba(0, 0, 0, 0.08);
  transition: transform 0.3s;
}

.theme-config-button:hover {
  transform: scale(1.1);
}

.theme-config-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.config-section {
  margin-bottom: 20px;
}

.config-section h3 {
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 500;
}

.theme-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.color-picker-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.color-preview {
  width: 100%;
  height: 24px;
  border-radius: 4px;
  margin-bottom: 8px;
}

.color-input-addon {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.color-input-addon input[type="color"] {
  width: 20px;
  height: 20px;
  border: none;
  padding: 0;
  background: none;
  cursor: pointer;
}

.color-demo {
  width: 24px;
  height: 24px;
  border-radius: 4px;
}

.bg-primary {
  background-color: var(--theme-primary);
}

.bg-success {
  background-color: var(--theme-success);
}

.bg-warning {
  background-color: var(--theme-warning);
}

.bg-error {
  background-color: var(--theme-error);
}
/* 主题颜色演示样式 */
.color-demo {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  display: inline-block;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.color-demo:hover {
  transform: scale(1.1);
}
</style>
